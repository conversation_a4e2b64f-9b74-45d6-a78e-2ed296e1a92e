<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/category_functions.php';
require_once 'includes/post_functions.php';

// Check if user is logged in
requireLogin();

// Get all categories
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Add New Post</h1>
        <a href="posts.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Posts
        </a>
    </div>

    <div class="row">
        <div class="col-md-9">
            <!-- Main Content Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="addPostForm" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control form-control-lg" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="content" class="form-label">Content</label>
                    <textarea id="content" name="content"></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="excerpt" class="form-label">Excerpt</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3"></textarea>
                            <div class="form-text">A short summary of the content. Will be used in blog listings and social media sharing.</div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="slug" class="form-label">URL Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug" form="addPostForm" required>
                        <div class="form-text">The URL-friendly version of the title. Will be auto-generated from title if left empty.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" form="addPostForm">
                        <div class="form-text">
                            <span id="metaTitleCount">0</span>/60 characters. The title that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="3" form="addPostForm"></textarea>
                        <div class="form-text">
                            <span id="metaDescCount">0</span>/160 characters. A brief description that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" form="addPostForm">
                        <div class="form-text">Comma-separated keywords related to the post. Example: travel, adventure, hiking</div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Search Engine Preview</h6>
                        </div>
                        <div class="card-body bg-light">
                            <div id="seoPreview" class="border p-3 rounded bg-white">
                                <div class="preview-title" style="color: #1a0dab; font-size: 18px;"></div>
                                <div class="preview-url" style="color: #006621; font-size: 14px;"></div>
                                <div class="preview-description" style="color: #545454; font-size: 14px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="addPostForm">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="addPostForm">
                            <i class="fas fa-save"></i> Save Post
                        </button>
                        <a href="posts.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Category -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                    <select class="form-select" id="category_id" name="category_id" form="addPostForm" required>
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                    <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*" form="addPostForm">
                    <div id="imagePreview" class="mt-3 text-center"></div>
                    <div class="form-text mt-2">Recommended size: 1200x630 pixels for optimal social sharing.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CKEditor 5 Configuration -->
<script src="https://cdn.ckeditor.com/ckeditor5/43.3.0/classic/ckeditor.js"></script>
<style>
    .ck-editor__editable_inline {
        min-height: 500px;
        border: 1px solid #e4e4e4;
        padding: 1.2em;
        font-size: 1.05em;
        background-color: #fff;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    .ck-content .image img {
        max-width: 100%;
        height: auto;
    }
    .template-dropdown {
        margin-top: 10px;
        margin-bottom: 10px;
    }
</style>

<script>
// Initialize CKEditor 5
document.addEventListener('DOMContentLoaded', function() {
    let editor;

    ClassicEditor.create(document.querySelector('#content'), {
        toolbar: {
            items: [
                'undo', 'redo', '|',
                'heading', '|',
                'bold', 'italic', 'underline', 'strikethrough', 'removeFormat', '|',
                'bulletedList', 'numberedList', 'outdent', 'indent', 'alignment', '|',
                'link', 'insertTable', 'blockQuote', 'imageInsert', '|',
                'sourceEditing'
            ]
        },
        language: 'en',
        image: {
            toolbar: [
                'imageTextAlternative',
                'toggleImageCaption',
                'imageStyle:inline',
                'imageStyle:block',
                'imageStyle:side'
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells'
            ]
        },
        placeholder: 'Start writing your travel story here...'
    })
    .then(newEditor => {
        editor = newEditor;
        window.editor = editor;

        // Add template dropdown
        const templateSelect = document.createElement('select');
        templateSelect.className = 'form-select template-dropdown';
        templateSelect.innerHTML = `
            <option value="">Insert Template</option>
            <option value="introduction">Standard Intro Text</option>
            <option value="itinerary">Day-by-Day Itinerary</option>
            <option value="comparison">Destination Comparison</option>
            <option value="gallery">Photo Gallery Layout</option>
            <option value="map">Location Map Integration</option>
            <option value="tips">Travel Tips Section</option>
            <option value="pricing">Pricing Details</option>
        `;

        templateSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                insertTemplate(editor, e.target.value);
                e.target.value = '';
            }
        });

        // Insert template dropdown after the editor
        const editorElement = document.querySelector('.ck-editor');
        if (editorElement) {
            editorElement.parentNode.insertBefore(templateSelect, editorElement.nextSibling);
        }

        console.log('CKEditor 5 initialized successfully!');
    })
    .catch(error => {
        console.error('CKEditor 5 initialization error:', error);
        alert('Failed to initialize the content editor. Please check the console for details.');
    });

    // Template insertion function
    function insertTemplate(editor, templateName) {
        const templates = {
            introduction: `<h2>Welcome to this destination</h2><p>Start your travel story with an engaging introduction here. Highlight the magic of your experience.</p>`,
            itinerary: `<h3>Day-by-Day Plan</h3><ol><li>Day 1: Arrival and orientation</li><li>Day 2: Explore major highlights</li><li>Day 3: Off-the-beaten-path adventures</li><li>Day 4: Departure</li></ol><p><strong>💡 Pro Tip:</strong> Include transportation details and local recommendations</p>`,
            comparison: `<h3>Destination Comparison</h3><table style="width:100%; border-collapse: collapse;"><thead><tr><th style="border: 1px solid #ddd; padding: 8px;">Feature</th><th style="border: 1px solid #ddd; padding: 8px;">Destination A</th><th style="border: 1px solid #ddd; padding: 8px;">Destination B</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 8px;">Cost</td><td style="border: 1px solid #ddd; padding: 8px;">$$</td><td style="border: 1px solid #ddd; padding: 8px;">$$$</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Best Time to Visit</td><td style="border: 1px solid #ddd; padding: 8px;">March-May</td><td style="border: 1px solid #ddd; padding: 8px;">September-November</td></tr></tbody></table>`,
            gallery: `<h3>Photo Gallery</h3><p>Insert your travel photos here with captions describing the experience.</p>`,
            map: `<h3>Location Map</h3><p>Add an embedded map or location details here to help readers find this destination.</p>`,
            tips: `<h3>Travel Tips</h3><ul><li>Arrive early to avoid crowds</li><li>Bring comfortable walking shoes</li><li>Carry a reusable water bottle</li><li>Respect local customs and traditions</li></ul>`,
            pricing: `<h3>Pricing Information</h3><h4>Sample Itinerary: 5 Days</h4><ul><li>Accommodation: $300-500</li><li>Food: $150-200</li><li>Admissions: $50-100</li><li><strong>Total: $500-800</strong></li></ul><p><em>All prices are per person and may vary based on season and booking time</em></p>`
        };

        if (templates[templateName]) {
            editor.model.change(writer => {
                const viewFragment = editor.data.processor.toView(templates[templateName]);
                const modelFragment = editor.data.toModel(viewFragment);
                editor.model.insertContent(modelFragment);
            });
        }
    }
});

    // Initialize character counters
    function updateCharCount(input, counter, limit) {
        const count = input.value.length;
        counter.textContent = count;
        counter.style.color = count > limit ? '#dc3545' : '#212529';
    }

    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const metaTitleInput = document.getElementById('meta_title');
    const metaTitleCount = document.getElementById('metaTitleCount');
    const metaDesc = document.getElementById('meta_description');
    const metaDescCount = document.getElementById('metaDescCount');

    // Title input handler
    titleInput.addEventListener('input', function(e) {
        const titleValue = e.target.value;

        // Update slug if empty or not manually edited
        if (!slugInput.value || slugInput.dataset.auto !== 'false') {
            slugInput.value = generateSlug(titleValue);
        }

        // Update meta title if empty
        if (!metaTitleInput.value) {
            metaTitleInput.value = titleValue;
            updateCharCount(metaTitleInput, metaTitleCount, 60);
        }

        // Update SEO preview
        updateSEOPreview();
    });

    // Meta title input handler
    metaTitleInput.addEventListener('input', function() {
        updateCharCount(this, metaTitleCount, 60);
        updateSEOPreview();
    });

    // Meta description input handler
    metaDesc.addEventListener('input', function() {
        updateCharCount(this, metaDescCount, 160);
        updateSEOPreview();
    });

    // Slug input handler
    slugInput.addEventListener('input', function() {
        this.dataset.auto = 'false';
        updateSEOPreview();
    });

    // Update SEO preview
    function updateSEOPreview() {
        const title = metaTitleInput.value || titleInput.value;
        const slug = slugInput.value;
        const desc = metaDesc.value || document.getElementById('excerpt').value;
        
        document.querySelector('.preview-title').textContent = title;
        document.querySelector('.preview-url').textContent = `${window.location.origin}/blog/${slug}`;
        document.querySelector('.preview-description').textContent = desc;
    }

    // Add excerpt input handler for SEO preview
    document.getElementById('excerpt').addEventListener('input', updateSEOPreview);

    // Form submission
    document.getElementById('addPostForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Get CKEditor 5 content
        if (window.editor && window.editor.getData) {
            formData.set('content', window.editor.getData());
        } else {
            formData.set('content', document.getElementById('content').value);
        }

        // Add meta data
        formData.set('meta_title', metaTitleInput.value || titleInput.value);
        formData.set('meta_description', metaDesc.value || document.getElementById('excerpt').value);

        fetch('<?php echo ADMIN_URL; ?>/ajax/add_post.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'posts.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the post');
        });
});

// Helper function to generate slug
function generateSlug(text) {
    if (!text) return '';

    return text
        .toLowerCase()
        .trim()
        .normalize('NFD') // Normalize unicode characters
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Image preview with size validation
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('featured_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    let warning = '';
                    if (this.width < 1200 || this.height < 630) {
                        warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (1200x630)</div>';
                    }
                    document.getElementById('imagePreview').innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100%;">
                        <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                        ${warning}
                    `;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
