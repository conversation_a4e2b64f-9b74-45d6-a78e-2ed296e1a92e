<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/category_functions.php';
require_once 'includes/post_functions.php';

// Check if user is logged in
requireLogin();

// Get all categories
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Add New Post</h1>
        <a href="posts.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Posts
        </a>
    </div>

    <div class="row">
        <div class="col-md-9">
            <!-- Main Content Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="addPostForm" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control form-control-lg" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="content" class="form-label">Content</label>
                    <textarea id="content" name="content"></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="excerpt" class="form-label">Excerpt</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3"></textarea>
                            <div class="form-text">A short summary of the content. Will be used in blog listings and social media sharing.</div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="slug" class="form-label">URL Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug" form="addPostForm" required>
                        <div class="form-text">The URL-friendly version of the title. Will be auto-generated from title if left empty.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" form="addPostForm">
                        <div class="form-text">
                            <span id="metaTitleCount">0</span>/60 characters. The title that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="3" form="addPostForm"></textarea>
                        <div class="form-text">
                            <span id="metaDescCount">0</span>/160 characters. A brief description that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" form="addPostForm">
                        <div class="form-text">Comma-separated keywords related to the post. Example: travel, adventure, hiking</div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Search Engine Preview</h6>
                        </div>
                        <div class="card-body bg-light">
                            <div id="seoPreview" class="border p-3 rounded bg-white">
                                <div class="preview-title" style="color: #1a0dab; font-size: 18px;"></div>
                                <div class="preview-url" style="color: #006621; font-size: 14px;"></div>
                                <div class="preview-description" style="color: #545454; font-size: 14px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="addPostForm">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="addPostForm">
                            <i class="fas fa-save"></i> Save Post
                        </button>
                        <a href="posts.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Category -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                    <select class="form-select" id="category_id" name="category_id" form="addPostForm" required>
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                    <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*" form="addPostForm">
                    <div id="imagePreview" class="mt-3 text-center"></div>
                    <div class="form-text mt-2">Recommended size: 1200x630 pixels for optimal social sharing.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- TinyMCE Configuration -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<style>
    .tox-tinymce {
        border: 1px solid #e4e4e4 !important;
        border-radius: 0.375rem !important;
    }
    .template-dropdown {
        margin-top: 10px;
        margin-bottom: 10px;
    }
</style>

<script>
// Global variables
let editor;
let titleInput, slugInput, metaTitleInput, metaTitleCount, metaDesc, metaDescCount;

// Template insertion function
function insertTemplate(templateName) {
    const templates = {
        introduction: `<h2>Welcome to this destination</h2><p>Start your travel story with an engaging introduction here. Highlight the magic of your experience.</p>`,
        itinerary: `<h3>Day-by-Day Plan</h3><ol><li>Day 1: Arrival and orientation</li><li>Day 2: Explore major highlights</li><li>Day 3: Off-the-beaten-path adventures</li><li>Day 4: Departure</li></ol><p><strong>💡 Pro Tip:</strong> Include transportation details and local recommendations</p>`,
        comparison: `<h3>Destination Comparison</h3><table style="width:100%; border-collapse: collapse;"><thead><tr><th style="border: 1px solid #ddd; padding: 8px;">Feature</th><th style="border: 1px solid #ddd; padding: 8px;">Destination A</th><th style="border: 1px solid #ddd; padding: 8px;">Destination B</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 8px;">Cost</td><td style="border: 1px solid #ddd; padding: 8px;">$$</td><td style="border: 1px solid #ddd; padding: 8px;">$$$</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Best Time to Visit</td><td style="border: 1px solid #ddd; padding: 8px;">March-May</td><td style="border: 1px solid #ddd; padding: 8px;">September-November</td></tr></tbody></table>`,
        gallery: `<h3>Photo Gallery</h3><p>Insert your travel photos here with captions describing the experience.</p>`,
        map: `<h3>Location Map</h3><p>Add an embedded map or location details here to help readers find this destination.</p>`,
        tips: `<h3>Travel Tips</h3><ul><li>Arrive early to avoid crowds</li><li>Bring comfortable walking shoes</li><li>Carry a reusable water bottle</li><li>Respect local customs and traditions</li></ul>`,
        pricing: `<h3>Pricing Information</h3><h4>Sample Itinerary: 5 Days</h4><ul><li>Accommodation: $300-500</li><li>Food: $150-200</li><li>Admissions: $50-100</li><li><strong>Total: $500-800</strong></li></ul><p><em>All prices are per person and may vary based on season and booking time</em></p>`
    };

    if (templates[templateName] && tinymce.activeEditor) {
        tinymce.activeEditor.insertContent(templates[templateName]);
    }
}

// Helper function to generate slug
function generateSlug(text) {
    if (!text) return '';

    return text
        .toLowerCase()
        .trim()
        .normalize('NFD') // Normalize unicode characters
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Initialize character counters
function updateCharCount(input, counter, limit) {
    const count = input.value.length;
    counter.textContent = count;
    counter.style.color = count > limit ? '#dc3545' : '#212529';
}

// Update SEO preview
function updateSEOPreview() {
    const title = metaTitleInput.value || titleInput.value;
    const slug = slugInput.value;
    const desc = metaDesc.value || document.getElementById('excerpt').value;

    document.querySelector('.preview-title').textContent = title;
    document.querySelector('.preview-url').textContent = `${window.location.origin}/blog/${slug}`;
    document.querySelector('.preview-description').textContent = desc;
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing TinyMCE...');

    // Initialize form elements
    titleInput = document.getElementById('title');
    slugInput = document.getElementById('slug');
    metaTitleInput = document.getElementById('meta_title');
    metaTitleCount = document.getElementById('metaTitleCount');
    metaDesc = document.getElementById('meta_description');
    metaDescCount = document.getElementById('metaDescCount');

    // Initialize TinyMCE
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic underline strikethrough | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | link image media table | code fullscreen help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        placeholder: 'Start writing your travel story here...',
        images_upload_url: '<?php echo ADMIN_URL; ?>/upload.php',
        automatic_uploads: true,
        file_picker_types: 'image',
        file_picker_callback: function (cb, value, meta) {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');

            input.onchange = function () {
                var file = this.files[0];
                var reader = new FileReader();
                reader.onload = function () {
                    var id = 'blobid' + (new Date()).getTime();
                    var blobCache = tinymce.activeEditor.editorUpload.blobCache;
                    var base64 = reader.result.split(',')[1];
                    var blobInfo = blobCache.create(id, file, base64);
                    blobCache.add(blobInfo);
                    cb(blobInfo.blobUri(), { title: file.name });
                };
                reader.readAsDataURL(file);
            };

            input.click();
        },
        setup: function (editor) {
            editor.on('init', function () {
                console.log('TinyMCE initialized successfully!');
                window.editor = editor;

                // Add template dropdown
                const templateSelect = document.createElement('select');
                templateSelect.className = 'form-select template-dropdown';
                templateSelect.innerHTML = `
                    <option value="">Insert Template</option>
                    <option value="introduction">Standard Intro Text</option>
                    <option value="itinerary">Day-by-Day Itinerary</option>
                    <option value="comparison">Destination Comparison</option>
                    <option value="gallery">Photo Gallery Layout</option>
                    <option value="map">Location Map Integration</option>
                    <option value="tips">Travel Tips Section</option>
                    <option value="pricing">Pricing Details</option>
                `;

                templateSelect.addEventListener('change', (e) => {
                    if (e.target.value) {
                        insertTemplate(e.target.value);
                        e.target.value = '';
                    }
                });

                // Insert template dropdown after the editor
                const editorElement = document.querySelector('.tox-tinymce');
                if (editorElement) {
                    editorElement.parentNode.insertBefore(templateSelect, editorElement.nextSibling);
                }
            });
        }
    });


    // Setup event listeners
    setupEventListeners();
});

function setupEventListeners() {
    // Title input handler
    titleInput.addEventListener('input', function(e) {
        const titleValue = e.target.value;

        // Update slug if empty or not manually edited
        if (!slugInput.value || slugInput.dataset.auto !== 'false') {
            slugInput.value = generateSlug(titleValue);
        }

        // Update meta title if empty
        if (!metaTitleInput.value) {
            metaTitleInput.value = titleValue;
            updateCharCount(metaTitleInput, metaTitleCount, 60);
        }

        // Update SEO preview
        updateSEOPreview();
    });

    // Meta title input handler
    metaTitleInput.addEventListener('input', function() {
        updateCharCount(this, metaTitleCount, 60);
        updateSEOPreview();
    });

    // Meta description input handler
    metaDesc.addEventListener('input', function() {
        updateCharCount(this, metaDescCount, 160);
        updateSEOPreview();
    });

    // Slug input handler
    slugInput.addEventListener('input', function() {
        this.dataset.auto = 'false';
        updateSEOPreview();
    });

    // Add excerpt input handler for SEO preview
    document.getElementById('excerpt').addEventListener('input', updateSEOPreview);

    // Form submission
    document.getElementById('addPostForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Get CKEditor 5 content
        if (window.editor && window.editor.getData) {
            formData.set('content', window.editor.getData());
        } else {
            formData.set('content', document.getElementById('content').value);
        }

        // Add meta data
        formData.set('meta_title', metaTitleInput.value || titleInput.value);
        formData.set('meta_description', metaDesc.value || document.getElementById('excerpt').value);

        fetch('<?php echo ADMIN_URL; ?>/ajax/add_post.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'posts.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the post');
        });
    });

    // Image preview with size validation
    document.getElementById('featured_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    let warning = '';
                    if (this.width < 1200 || this.height < 630) {
                        warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (1200x630)</div>';
                    }
                    document.getElementById('imagePreview').innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100%;">
                        <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                        ${warning}
                    `;
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
