<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/category_functions.php';
require_once 'includes/post_functions.php';

// Check if user is logged in
requireLogin();

// Get all categories
$categories = getAllCategories();

// Include header
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Add New Post</h1>
        <a href="posts.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Posts
        </a>
    </div>

    <div class="row">
        <div class="col-md-9">
            <!-- Main Content Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form id="addPostForm" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control form-control-lg" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="content" class="form-label">Content</label>
                    <textarea id="content" name="content"></textarea>
                        </div>

                        <div class="mb-4">
                            <label for="excerpt" class="form-label">Excerpt</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3"></textarea>
                            <div class="form-text">A short summary of the content. Will be used in blog listings and social media sharing.</div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="slug" class="form-label">URL Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug" form="addPostForm" required>
                        <div class="form-text">The URL-friendly version of the title. Will be auto-generated from title if left empty.</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" form="addPostForm">
                        <div class="form-text">
                            <span id="metaTitleCount">0</span>/60 characters. The title that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="3" form="addPostForm"></textarea>
                        <div class="form-text">
                            <span id="metaDescCount">0</span>/160 characters. A brief description that appears in search engine results.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" form="addPostForm">
                        <div class="form-text">Comma-separated keywords related to the post. Example: travel, adventure, hiking</div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Search Engine Preview</h6>
                        </div>
                        <div class="card-body bg-light">
                            <div id="seoPreview" class="border p-3 rounded bg-white">
                                <div class="preview-title" style="color: #1a0dab; font-size: 18px;"></div>
                                <div class="preview-url" style="color: #006621; font-size: 14px;"></div>
                                <div class="preview-description" style="color: #545454; font-size: 14px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Publish</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" form="addPostForm">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" form="addPostForm">
                            <i class="fas fa-save"></i> Save Post
                        </button>
                        <a href="posts.php" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Category -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                    <select class="form-select" id="category_id" name="category_id" form="addPostForm" required>
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                    <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*" form="addPostForm">
                    <div id="imagePreview" class="mt-3 text-center"></div>
                    <div class="form-text mt-2">Recommended size: 1200x630 pixels for optimal social sharing.</div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- CKEditor 5 Full Featured Configuration -->
<link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/43.3.0/classic/ckeditor.css">
<style>
    .ck-editor__editable_inline {
        min-height: 500px;
        border: 1px solid #e4e4e4;
        padding: 1.2em;
        font-size: 1.05em;
        background-color: #fff;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
    .ck-content .image img {
        max-width: 100%;
        height: auto;
    }
    .ck-content ul.cke_list_bullet {
        list-style-type: disc;
    }
</style>

<?php if(!defined('CKEDITOR_LOADED')): ?>
<?php define('CKEDITOR_LOADED', true); ?>
<script src="https://cdn.ckeditor.com/ckeditor5/43.3.0/classic/ckeditor.js"></script>
<script>
    // Define custom styles for travel blog content
    const customStyles = {
        styles: {
            'destination-header': 'Destination Header',
            'travel-tip': 'Travel Tip Box',
            'price-info': 'Price Info',
            'itinerary-day': 'Itinerary Day',
            'photo-gallery': 'Photo Gallery',
            'travel-quote': 'Quote Block',
            'highlight': 'Yellow Highlight',
            'location': 'Location Name',
            'important-note': 'Important Note',
            'price': 'Price'
        },
        templates: {
            'introduction': 'Standard Intro Text',
            'itinerary': 'Day-by-Day Itinerary',
            'comparison': 'Destination Comparison',
            'gallery': 'Photo Gallery Layout',
            'map': 'Location Map Integration',
            'tips': 'Travel Tips Section',
            'pricing': 'Pricing Details'
        }
    };

    // Initialize CKEditor 5 with travel-focused features
    document.addEventListener('DOMContentLoaded', function() {
        let editor;
        
        ClassicEditor.create(document.querySelector('#content'), {
            toolbar: {
                items: [
                    'undo', 'redo', '|',
                    'heading', 'style', '|',
                    'bold', 'italic', 'underline', 'strikethrough', 'formatPainter', 'removeFormat', '|',
                    'fontColor', 'fontBackgroundColor', '|',
                    'fontSize', 'fontFamily', '|',
                    'bulletedList', 'numberedList', 'todoList', 'outdent', 'indent', 'horizontalLine', 'alignment', '|',
                    'link', 'insertTable', 'blockQuote', 'codeBlock', 'htmlEmbed', 'mediaEmbed', 'imageInsert', 
                    'fileInsert', '|', 'specialCharacters', 'pageBreak', 'sourceEditing', '|',
                    'highlight', 'exportPdf', 'fullScreen', 'wordCount'
                ]
            },
            language: 'en',
            image: {
                toolbar: [
                    'imageTextAlternative', 
                    'toggleImageCaption', 
                    'imageStyle:inline', 
                    'imageStyle:block', 
                    'imageStyle:side',
                    'linkImage'
                ],
                upload: {
                    types: ['jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff']
                }
            },
            table: {
                contentToolbar: [
                    'tableColumn', 
                    'tableRow', 
                    'mergeTableCells',
                    'tableCellProperties',
                    'tableProperties'
                ]
            },
            placeholder: 'Start writing your travel story here...',
            styles: {
                classes: Object.keys(customStyles.styles)
            },
            stylesDropdown: {
                classes: Object.keys(customStyles.styles)
            },
            codeBlock: {
                languages: [
                    { language: 'plaintext', label: 'Plain text' },
                    { language: 'javascript', label: 'JavaScript' },
                    { language: 'html', label: 'HTML' },
                    { language: 'css', label: 'CSS' },
                    { language: 'markdown', label: 'Markdown' }
                ]
            },
            mediaEmbed: {
                previewsInData: true
            },
            simpleUpload: {
                uploadUrl: '<?php echo ADMIN_URL; ?>/upload.php',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }
        })
        .then(newEditor => {
            editor = newEditor;
            window.editor = editor;
            
            // Add template dropdown
            const templateSelect = document.createElement('select');
            templateSelect.className = 'form-select mt-3';
            templateSelect.innerHTML = `
                <option value="">Insert Template</option>
                ${Object.entries(customStyles.templates).map(([key, value]) => 
                    `<option value="${key}">${value}</option>`).join('')}
            `;
            
            templateSelect.addEventListener('change', (e) => {
                if (e.target.value) {
                    insertTemplate(editor, e.target.value);
                    e.target.value = '';
                }
            });
            
            editor.ui.view.toolbar.element.after(templateSelect);
        })
        .catch(error => {
            console.error('CKEditor 5 initialization error:', error);
            alert('Failed to initialize the content editor. Please check the console for details.');
        });
        
        // Template insertion function
        function insertTemplate(editor, templateName) {
            const templates = {
                introduction: `
                    <section class="intro-section">
                        <h2>Welcome to this destination</h2>
                        <p>Start your travel story with an engaging introduction here. Highlight the magic of your experience.</p>
                    </section>
                `,
                itinerary: `
                    <section class="itinerary">
                        <h3>Day-by-Day Plan</h3>
                        <ol>
                            <li>Day 1: Arrival and orientation</li>
                            <li>Day 2: Explore major highlights</li>
                            <li>Day 3: Off-the-beaten-path adventures</li>
                            <li>Day 4: Departure</li>
                        </ol>
                        <div class="tip-box">💡 Pro Tip: Include transportation details and local recommendations</div>
                    </section>
                `,
                comparison: `
                    <section class="comparison">
                        <h3>Destination Comparison</h3>
                        <table class="destination-comparison" style="width:100%">
                            <thead>
                                <tr>
                                    <th>Feature</th>
                                    <th>Destination A</th>
                                    <th>Destination B</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Cost</td>
                                    <td>$$</td>
                                    <td>$$$</td>
                                </tr>
                                <tr>
                                    <td>Best Time to Visit</td>
                                    <td>March-May</td>
                                    <td>September-November</td>
                                </tr>
                                <tr>
                                    <td>Family Friendly</td>
                                    <td>✅</td>
                                    <td>❌</td>
                                </tr>
                            </tbody>
                        </table>
                    </section>
                `,
                gallery: `
                    <section class="photo-gallery">
                        <h3>Photo Gallery</h3>
                        <div class="gallery-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 1rem; margin: 1rem 0;">
                            <figure class="gallery-item">
                                <img src="/uploads/posts/placeholder.jpg" />
                                <figcaption>Image caption</figcaption>
                            </figure>
                        </div>
                    </section>
                `,
                map: `
                    <div class="map-container" style="margin: 1.5rem 0;">
                        <div class="map-embed" style="position: relative; padding-bottom: 75%; height: 0;">
                            <iframe src="https://maps.google.com/maps?q=Location&t=&z=13&ie=UTF8&iwloc=&output=embed" 
                                    frameborder="0" 
                                    scrolling="no" 
                                    marginheight="0" 
                                    marginwidth="0"
                                    style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></iframe>
                        </div>
                    </div>
                `,
                tips: `
                    <section class="travel-tips">
                        <h3>Travel Tips</h3>
                        <ul>
                            <li>Arrive early to avoid crowds</li>
                            <li>Bring comfortable walking shoes</li>
                            <li>Carry a reusable water bottle</li>
                            <li>Respect local customs and traditions</li>
                        </ul>
                    </section>
                `,
                pricing: `
                    <section class="pricing-details">
                        <h3>Pricing Information</h3>
                        <div class="price-box">
                            <h4>Sample Itinerary: 5 Days</h4>
                            <ul>
                                <li>Accommodation: $300-500</li>
                                <li>Food: $150-200</li>
                                <li>Admissions: $50-100</li>
                                <li>Total: $500-800</li>
                            </ul>
                            <div class="note">All prices are per person and may vary based on season and booking time</div>
                        </div>
                    </section>
                `
            };
            
            if (templates[templateName]) {
                editor.model.change(writer => {
                    const position = editor.model.document.selection.getFirstPosition();
                    editor.model.insertContent(writer.createRawElement('div', {}, viewFragment => {
                        const parsed = new DOMParser().parseFromString(templates[templateName], 'text/html');
                        return viewFragment.fromFragment(parsed.body.firstChild, viewFragment, writer);
                    }), position);
                });
            }
        }
    });
</script>
<?php endif; ?>

<script>
    // Initialize CKEditor 4 with full feature set
    document.addEventListener('DOMContentLoaded', function() {
        // Reference to the editor instance
        let editor;
        
        // Configure CKEditor
        CKEDITOR.config.height = 500;
        CKEDITOR.config.width = '100%';
        CKEDITOR.config.toolbarCanCollapse = true;
        CKEDITOR.config.extraPlugins = 'codesnippet,colorbutton,font,justify,uploadimage,templates,emoji,tableresize,wordcount,autogrow,autocomplete,mentions,formatindentlist,pagebreak,stylescombo,language,print,pastefromgdocs,html5audio,html5video,iframe,embedbase,mathjax,find,liststyle,exportpdf,LocationMap,WeatherWidget,AttractionsGallery';
        CKEDITOR.config.removePlugins = 'image';
        CKEDITOR.config.allowedContent = true; // Disable content filtering
        CKEDITOR.config.removeButtons = ''; // Enable all buttons
        CKEDITOR.config.codeSnippet_theme = 'monokai_sublime';
        CKEDITOR.config.wordcount = {
            showParagraphs: true,
            showWordCount: true,
            showCharCount: true,
            countSpacesAsChars: false,
            countHTML: false
        };
        CKEDITOR.config.autogrow_minHeight = 500;
        CKEDITOR.config.autogrow_maxHeight = 800;
        CKEDITOR.config.filebrowserUploadUrl = '<?php echo ADMIN_URL; ?>/upload.php';
        CKEDITOR.config.filebrowserImageUploadUrl = '<?php echo ADMIN_URL; ?>/upload.php?type=image';
        CKEDITOR.config.contentsCss = ['<?php echo ADMIN_URL; ?>/assets/css/editor-styles.css']; // Apply custom styles to editor content
        
        // Enhanced toolbar with travel-specific tools
        CKEDITOR.config.toolbar = [
            { name: 'document', items: ['Source', 'Save', 'NewPage', 'ExportPdf', 'Preview', 'Print', 'Templates'] },
            { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
            { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll', '-', 'Scayt'] },
            { name: 'forms', items: ['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField'] },
            '/',
            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'CopyFormatting', 'RemoveFormat'] },
            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl', 'Language'] },
            { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
            { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe', 'Mathjax', 'CodeSnippet', 'Emoji', 'Html5audio', 'Html5video'] },
            '/',
            { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
            { name: 'colors', items: ['TextColor', 'BGColor'] },
            { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
            '/',
            { name: 'travel', items: ['LocationMap', 'WeatherWidget', 'AttractionsGallery'] }
        ];

        // Custom styles for travel blog
        CKEDITOR.stylesSet.add('travel_styles', [
            // Block level styles
            { name: 'Destination Header', element: 'h2', attributes: { 'class': 'destination-header' } },
            { name: 'Travel Tip Box', element: 'div', attributes: { 'class': 'travel-tip' } },
            { name: 'Price Info', element: 'div', attributes: { 'class': 'price-info' } },
            { name: 'Itinerary Day', element: 'div', attributes: { 'class': 'itinerary-day' } },
            { name: 'Photo Gallery', element: 'div', attributes: { 'class': 'photo-gallery' } },
            { name: 'Quote Block', element: 'blockquote', attributes: { 'class': 'travel-quote' } },
            
            // Inline styles
            { name: 'Highlight', element: 'span', attributes: { 'class': 'highlight' } },
            { name: 'Location Name', element: 'span', attributes: { 'class': 'location' } },
            { name: 'Important Note', element: 'span', attributes: { 'class': 'important-note' } },
            { name: 'Price', element: 'span', attributes: { 'class': 'price' } }
        ]);
        CKEDITOR.config.stylesSet = 'travel_styles';
        
        // Templates for common travel blog sections
        CKEDITOR.config.templates = 'travel';
        CKEDITOR.config.templates_files = ['<?php echo ADMIN_URL; ?>/assets/js/travel-templates.js'];
        
        // Initialize the editor
        editor = CKEDITOR.replace('content', {
            customConfig: '',
            on: {
                instanceReady: function(evt) {
                    console.log('CKEditor is ready!');
                    // Apply custom styles to editor content
                    this.document.getBody().addClass('cke_editable');
                }
            }
        });
    });

    // Initialize character counters
    function updateCharCount(input, counter, limit) {
        const count = input.value.length;
        counter.textContent = count;
        counter.style.color = count > limit ? '#dc3545' : '#212529';
    }

    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const metaTitleInput = document.getElementById('meta_title');
    const metaTitleCount = document.getElementById('metaTitleCount');
    const metaDesc = document.getElementById('meta_description');
    const metaDescCount = document.getElementById('metaDescCount');

    // Title input handler
    titleInput.addEventListener('input', function(e) {
        const titleValue = e.target.value;

        // Update slug if empty or not manually edited
        if (!slugInput.value || slugInput.dataset.auto !== 'false') {
            slugInput.value = generateSlug(titleValue);
        }

        // Update meta title if empty
        if (!metaTitleInput.value) {
            metaTitleInput.value = titleValue;
            updateCharCount(metaTitleInput, metaTitleCount, 60);
        }

        // Update SEO preview
        updateSEOPreview();
    });

    // Meta title input handler
    metaTitleInput.addEventListener('input', function() {
        updateCharCount(this, metaTitleCount, 60);
        updateSEOPreview();
    });

    // Meta description input handler
    metaDesc.addEventListener('input', function() {
        updateCharCount(this, metaDescCount, 160);
        updateSEOPreview();
    });

    // Slug input handler
    slugInput.addEventListener('input', function() {
        this.dataset.auto = 'false';
        updateSEOPreview();
    });

    // Update SEO preview
    function updateSEOPreview() {
        const title = metaTitleInput.value || titleInput.value;
        const slug = slugInput.value;
        const desc = metaDesc.value || document.getElementById('excerpt').value;
        
        document.querySelector('.preview-title').textContent = title;
        document.querySelector('.preview-url').textContent = `${window.location.origin}/blog/${slug}`;
        document.querySelector('.preview-description').textContent = desc;
    }

    // Add excerpt input handler for SEO preview
    document.getElementById('excerpt').addEventListener('input', updateSEOPreview);

    // Form submission
    document.getElementById('addPostForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        // Get CKEditor 4 content
        // Get CKEditor 5 content
        if (window.editor && window.editor.isReady) {
            formData.set('content', editor.getData());
        } else {
            formData.set('content', document.getElementById('content').value);
        }
        
        // Add meta data
        formData.set('meta_title', metaTitleInput.value || titleInput.value);
        formData.set('meta_description', metaDesc.value || document.getElementById('excerpt').value);
        
        fetch('<?php echo ADMIN_URL; ?>/ajax/add_post.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = 'posts.php';
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the post');
        });
    });
});

// Helper function to generate slug
function generateSlug(text) {
    if (!text) return '';
    
    return text
        .toLowerCase()
        .trim()
        .normalize('NFD') // Normalize unicode characters
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Image preview with size validation
document.getElementById('featured_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = new Image();
            img.onload = function() {
                let warning = '';
                if (this.width < 1200 || this.height < 630) {
                    warning = '<div class="alert alert-warning mt-2">Image is smaller than recommended size (1200x630)</div>';
                }
                document.getElementById('imagePreview').innerHTML = `
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100%;">
                    <div class="text-muted mt-1">Size: ${this.width}x${this.height}</div>
                    ${warning}
                `;
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
